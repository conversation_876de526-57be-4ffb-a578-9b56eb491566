import Vue from 'vue';
import App from './App.vue';
import router from './routes';
import Element<PERSON> from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

import ElementUIX from 'vue-element-ui-x';

Vue.use(ElementUI);
Vue.use(ElementUIX);
// Vue.config.productionTip = false;

import * as ysdChatUi from 'ysd-chat-ui';
ysdChatUi.api.AIAgrentInitzer.setApiPrefixInit(CONF_N8N_API_PREFIX);
ysdChatUi.api.AIAgrentInitzer.setNormalChat(CONF_N8N_NORMAL_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setThinking(CONF_N8N_THINKING_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setDeepThinkChat(CONF_N8N_DEEP_THINKING_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setDeepThinkChatSingle(CONF_N8N_DEEP_THINKING_SINGLE_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setTitle(CONF_N8N_TITLE_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setUploadFiles(CONF_N8N_UPLOAD_FILES_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setUploadFile(CONF_N8N_UPLOAD_FILE_WORKFLOW);
ysdChatUi.api.AIAgrentInitzer.setSessionIdTest(CONF_SESSION_ID_TEST);

let vueInst = new Vue({
  router,
  render: (h) => h(App),
}).$mount('#app');

import { VueHelper as vh } from "ysd-vue-tools";
vh.initVueInst(vueInst);

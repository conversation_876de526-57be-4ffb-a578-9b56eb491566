
// n8n 的api接口前缀
// const CONF_N8N_API_PREFIX = 'http://2m234l1314.imwork.net/n8n/webhook/';
const CONF_N8N_API_PREFIX = 'http://192.168.78.250:5678/';
//  正常对话工作流
// const CONF_N8N_NORMAL_WORKFLOW = '6ad83aed-0cd8-4784-b38f-0324cca53a43';
const CONF_N8N_NORMAL_WORKFLOW = '6ad83aed-0cd8-4784-b38f-0324cca53a43';
// 推理思考工作流
const CONF_N8N_THINKING_WORKFLOW = '5783c548-c6a4-424e-9717-c9056a5816a0';
// 深度思考工作流
const CONF_N8N_DEEP_THINKING_WORKFLOW = '3011fc41-2fd7-452a-b8d6-f3b303c88b0f';
// 深度思考单一模型端到端工作流
const CONF_N8N_DEEP_THINKING_SINGLE_WORKFLOW = '0038f754-f6eb-4c18-8b5c-6ed29c55a7e8';
// 生成标题工作流
const CONF_N8N_TITLE_WORKFLOW = 'ca006a5e-f73a-40d2-bc4e-e6dc28529352';
// 上传附件工作流（多文件）
const CONF_N8N_UPLOAD_FILES_WORKFLOW = 'c50a2c61-5c2e-4395-8318-6b210d0c8cca';
// 上传附件工作流（单文件）
const CONF_N8N_UPLOAD_FILE_WORKFLOW = 'b45b7541-8b83-4f6b-800c-d5c0cf6242f6';

// 会话ID 测试用
const CONF_SESSION_ID_TEST = '1234567890';
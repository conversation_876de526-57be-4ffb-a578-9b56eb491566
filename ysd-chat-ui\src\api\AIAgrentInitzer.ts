
import { N8NInitzer as N8N } from "./N8NInitzer";

let _NormalChat = "";
let _Title = "";
let _Thinking = "";
let _DeepThinkChat = "";
// let _sessionIdTest = "";
let _DeepThinkChatSingle = "";
let _UploadFiles = "";
let _UploadFile = "";

/** n8n初始化器 */
export class AIAgrentInitzer {

    /**
     * 初始化n8n的常规模型对话工作流
     */
    static setNormalChat(NormalChat: string) {
        _NormalChat = NormalChat;
    }

    /**
     * 初始化n8n的推理思考工作流
     */
    static setThinking(thinking: string) {
        _Thinking = thinking;
    }

    /**
     * 初始化深度思考的对话工作流
     */
    static setDeepThinkChat(deepThinkChat: string) {
        _DeepThinkChat = deepThinkChat;
    }

    /**
     * 初始化深度思考的单一模型端到端工作流
     */
    static setDeepThinkChatSingle(deepThinkChatSingle: string) {
        _DeepThinkChatSingle = deepThinkChatSingle;
    }

    /**
     * 初始化n8n的生成标题工作流
     */
    static setTitle(title: string) {
        _Title = title;
    }
    

    /**
     * 初始化n8n的附件上传工作流
     */
    static setUploadFiles(uploadFile: string) {
        _UploadFiles = uploadFile;
    }

    /**
     * 初始化n8n的附件上传工作流（单文件）
     */
    static setUploadFile(uploadFile: string) {
        _UploadFile = uploadFile;
    }

    // /**
    //  * 获取n8n的正常对话工作流
    //  * @returns 
    //  */
    // static getNormalChat() {
    //     return _NormalChat;
    // }

    // /**
    //  * 获取n8n的推理思考工作流
    //  * @returns 
    //  */
    // static getThinking() {
    //     return _Thinking;
    // }

    // /**
    //  * 获取n8n的深度思考工作流
    //  * @returns 
    //  */
    // static getDeepThinkChat() {
    //     return _DeepThinkChat;
    // }

    // /**
    //  * 获取n8n的深度思考的单一模型端到端工作流
    //  * @returns 
    //  */
    // static getDeepThinkChatSingle() {
    //     return _DeepThinkChatSingle;
    // }

    // /**
    //  * 获取n8n的生成标题工作流
    //  * @returns 
    //  */
    // static getN8nTitleWorkflow() {
    //     return _Title;
    // }

    // /**
    //  * 获取n8n的会话ID
    //  * @returns 
    //  */ 
    // static getSessionIdTest() {
    //     return _sessionIdTest;
    // }

    // /**
    //  * 设置n8n的会话ID
    //  */
    // static setSessionIdTest(sessionIdTest: string) {
    //     _sessionIdTest = sessionIdTest;
    // }

    // /**
    //  * 获取n8n的附件上传工作流(多文件)
    //  * @returns 
    //  */
    // static getUploadFiles() {
    //     return _UploadFiles;
    // }

    // /**
    //  * 获取n8n的附件上传工作流（单文件）
    //  * @returns 
    //  */
    // static getUploadFile() {
    //     return _UploadFile;
    // }

    /**
     * 获取n8n的正常对话接口链接
     */
    static getNormalApi(): string {
        return `${N8N.getApiPrefix()}${_NormalChat}`;
    }

    /**
     * 获取n8n的生成标题接口链接
     */
    static getTitleApi(): string {
        return `${N8N.getApiPrefix()}${_Title}`;
    }

    /**
     * 获取n8n的推理思考接口链接
     */
    static getThinkingApi(): string {
        return `${N8N.getApiPrefix()}${_Thinking}`;
    }

    /**
     * 获取n8n的深度思考接口链接
     */
    static getDeepThinkChatApi(): string {
        return `${N8N.getApiPrefix()}${_DeepThinkChat}`;
    }

    /**
     * 获取n8n的深度思考的单一模型端到端工作流
     */
    static getDeepThinkChatSingleApi(): string {
        return `${N8N.getApiPrefix()}${_DeepThinkChatSingle}`;
    }

    /**
     * 获取n8n的附件上传工作流
     */
    static getUploadFilesApi(): string {
        // 测试
        return `http://**************:5678/webhook-test/${_UploadFiles}`;
        // return `${N8N.getApiPrefix()}${this.getUploadFiles()}`;
    }

    /**
     * 获取n8n的附件上传工作流（单文件）
     */
    static getUploadFileApi(): string {
        // return `http://**************:5678/webhook-test/${this.getUploadFile()}`;
        return `${N8N.getApiPrefix()}${_UploadFile}`;
    }
};

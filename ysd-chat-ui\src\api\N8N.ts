
import { AIAgrentInitzer } from './AIAgrentInitzer';
import * as Model from "../model";

export class N8N {
    private static instance: N8N | null = null;


    /**
     * 发送消息 (常规模型)
     * @param msg 消息内容
     * @param fileContent 附件内容
     * @param sessionUid 会话ID 不填则创建新的会话
     * @returns 
     */
    async sendMsg(msg: string, user: string, fileContent: string = '', sessionUid: string = ''): Promise<Model.IN8NResult> {
        let url = AIAgrentInitzer.getNormalApi();
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            formData.append('query', msg);
            if (user != '') formData.append('user', user);
            if (fileContent != '') formData.append('fileContent', fileContent);
            formData.append('sessionId', sessionUid);

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let data = await response.json() as Model.IN8NResult;
            return data;
        } 
        catch (error) {
            console.error('Error sending message to n8n:', error);
            return {
                output: '抱歉，服务器出现错误，请稍后再试。'
            };
        }
    }

    /**
     * 发送消息 推理思考
     * @param msg 消息内容
     * @returns 
     */
    async sendMsgThinking(msg: string): Promise<Model.IN8NResult> {
        let url = AIAgrentInitzer.getThinkingApi();
        let res = {thinkingArray: [], output: ''};
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            formData.append('query', msg);

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let data = await response.json() as {output: string};
            // 去除头尾<thinking>，包括可能的换行符
            res.output = data.output.replace(/^\s*<thinking>|<\/thinking>\s*$/g, '');
            return res;
        }
        catch (error) {
            console.error('Error sending message to n8n:', error);
            res.output = '抱歉，服务器出现错误，请稍后再试。';
            return res;
        }
    }

    /**
     * 发送消息 (推理模型 深度思考)
     * @param msg 消息内容
     * @param reasonContent 推理内容
     * @param sessionId 会话ID 不填则创建新的会话
     * @returns 
     */
    async sendMsgDeepThinkChat(msg: string, reasonContent: string, sessionId: string = ''): Promise<Model.IN8NResult> {
        let url = AIAgrentInitzer.getDeepThinkChatApi();
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            formData.append('query', msg);
            formData.append('reasonContent', reasonContent);
            // 测试用
            formData.append('sessionId', "");

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let data = await response.json() as Model.IN8NResult;
            return data;
        }
        catch (error) {
            console.error('Error sending message to n8n:', error);
            return {
                output: '抱歉，服务器出现错误，请稍后再试。'
            };
        }
    }

    /**
     * 深度思考回答 (单一模型端到端)
     * 
     * @param msg 消息内容
     * @param fileContent 附件内容
     * @param sessionId 会话ID 不填则创建新的会话
     * @returns 
     */
    async deepThinkChatSingle(msg: string, fileContent: string = '', sessionId: string = ''): Promise<Model.IDeepThinkChatSingle> {
        let url = AIAgrentInitzer.getDeepThinkChatSingleApi();
        let res = {thinkingArray: [], thinking: '', answer: ''};
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            formData.append('query', msg);
            formData.append('fileContent', fileContent);
            // // 测试用
            // formData.append('sessionId', N8NInitzer.getSessionIdTest());

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let data = await response.json() as Model.IN8NResult;
            // 提取思考内容 <thinking>...</thinking>，使用 [\s\S]* 来匹配包括换行符在内的所有字符
            res.thinking = data.output.match(/<thinking>([\s\S]*?)<\/thinking>/)?.[1]?.trim() || '';
            // 提取最终答案 </thinking>后的内容
            res.answer = data.output.match(/<\/thinking>([\s\S]*?)$/)?.[1]?.trim() || '';

            return res;
        }
        catch (error) {
            console.error('Error sending message to n8n:', error);
            res.answer = '抱歉，服务器出现错误，请稍后再试。';
            return res;
        }
    }

    /**
     * 生成标题
     * @param content 消息内容
     * @returns 
     */
    async generateTitle(content: string): Promise<{text: string}> {
        let url = AIAgrentInitzer.getTitleApi();
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            formData.append('content', content);

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });         
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let data = await response.json();

            return {
                text: data.text
            };
        }
        catch (error) {
            console.error('Error generating title:', error);
            return {
                text: ""
            };
        }
    }

    /**
     * 上传附件（多文件) 暂不可用
     * @param files 附件列表
     * @returns 
     */
    async uploadFiles(files: FileList, fileInfos: Model.IFileInfo[]): Promise<string[]> {
        let url = AIAgrentInitzer.getUploadFilesApi();
        try {
            // 创建 FormData 对象
            let formData = new FormData();
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
                formData.append('fileInfos', JSON.stringify(fileInfos[i]));
            }

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let res = await response.json() as {data: string[]};
            return res.data;
        }
        catch (error) {
            console.error('Error uploading files:', error);
            return [];
        }
    }

    /**
     * 上传附件（单文件)
     */ 
    async uploadFile(file: File, fileInfo: Model.IFileInfo): Promise<string> {
        let url = AIAgrentInitzer.getUploadFileApi();
        try {
            // 创建 FormData 对象   
            let formData = new FormData();
            formData.append('file', file);
            formData.append('fileInfo', JSON.stringify(fileInfo));

            let response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Accept': '*/*',
                },
                body: formData
            });
            if (response.ok == false) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            let res = await response.json() as {data: string};
            return res.data;
        }
        catch (error) {
            console.error('Error uploading file:', error);
            return '';
        }
    }

    static getInstance(): N8N {
        this.instance ??= new N8N();
        return this.instance;
    }
    
};
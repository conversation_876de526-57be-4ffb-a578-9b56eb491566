import { N8NInitzer as N8N } from "./N8NInitzer";

let _list = "";
let _create = "";
let _update = "";

/** 会话初始化器 */
export class SessionInitzer {
    /**
     * 初始化会话列表接口地址
     */
    static setList(list: string) {
        _list = list;
    }

    /**
     * 初始化创建会话接口地址
     */
    static setCreate(create: string) {
        _create = create;
    }

    /**
     * 初始化更新会话接口地址
     */
    static setUpdate(update: string) {
        _update = update;
    }

    /**
     * 获取会话列表接口地址
     * @returns 
     */
    static getList() {
        return `${N8N.getApiPrefix()}${_list}`;
    }

    /**
     * 获取创建会话接口地址
     * @returns 
     */
    static getCreate() {
        return `${N8N.getApiPrefix()}${_create}`;
    }

    /**
     * 获取更新会话接口地址
     * @returns 
     */
    static getUpdate() {
        return `${N8N.getApiPrefix()}${_update}`;
    }
};
import { N8NInitzer as N8N } from "./N8NInitzer";

let _history = "";
let _del = "";

export class MessageInitzer {
    /**
     * 初始化获取历史消息接口地址
     */
    static setHistory(history: string) {
        _history = history;
    }

    /**
     * 初始化删除消息接口地址
     */
    static setDel(del: string) {
        _del = del;
    }

    /**
     * 获取获取历史消息接口地址
     * @returns 
     */
    static getHistory() {
        return `${N8N.getApiPrefix()}${_history}`;
    }

    /**
     * 获取删除消息接口地址
     * @returns 
     */
    static getDel() {
        return `${N8N.getApiPrefix()}${_del}`;
    }
}